import os
import boto3
from loguru import logger
from datetime import datetime
from botocore.exceptions import ClientError

# --- Configuration ---
RESUME_PROCESSOR_TABLE_NAME = os.environ.get("RESUME_PROCESSOR_TABLE_NAME", "Digiverify-Resume-PII-Processor-Table")
REGION = os.environ.get("AWS_REGION", "us-east-1")

# --- Initialize DynamoDB Client ---
dynamodb = boto3.resource('dynamodb', region_name=REGION)
table = dynamodb.Table(RESUME_PROCESSOR_TABLE_NAME)

def fetch_item_by_job_id(job_id):
    try:
        response = table.get_item(Key={"job_id": job_id})
        item = response.get("Item")

        if item:
            logger.info("✅ Item found:")
            return item
        else:
            logger.error("❌ No item found with the given job_id.")
            return None

    except ClientError as e:
        logger.error(f"❌ Error fetching item: {e.response['Error']['Message']}")
        return None

def update_status_to_completed(job_id, response):
    try:
        current_timestamp = datetime.utcnow().isoformat() + "Z"
        response = table.update_item(
            Key={"job_id": job_id},
            UpdateExpression="SET #s = :status, updated_at = :timestamp, processed_response = :response",
            ExpressionAttributeNames={"#s": "status"},
            ExpressionAttributeValues={
                ":status": "completed",
                ":timestamp": current_timestamp,
                ":response": response["response"] if "response" in response else response
            },
            ReturnValues="UPDATED_NEW"
        )
        logger.info("✅ Data updated to 'completed':", response["Attributes"])
    except ClientError as e:
        logger.error(f"❌ Error updating item: {e.response['Error']['Message']}")