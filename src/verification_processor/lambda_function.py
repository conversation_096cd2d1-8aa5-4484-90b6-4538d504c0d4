"""
AWS Lambda handler for background verification processing
"""
import os
import json
from typing import Dict, Any, Optional
from loguru import logger
import sys
from pathlib import Path

from prompt_engineering.process_verification import process_background_verification

# Configure logger for Lambda
logger.remove()
logger.add(sys.stdout, level="INFO", format="{time} | {level} | {message}")

class LambdaVerificationProcessor:
    """
    Main processor for Lambda-based verification
    """

    def __init__(self):
        self.mock_data = None

    def process_event(self, event: Dict[str, Any], context: Any) -> Dict[str, Any]:
        """
        Process Lambda event and return result
        """
        try:
            logger.info(f"Lambda invoked with event: {json.dumps(event, default=str)}")
            
            # Handle SQS event
            if 'Records' in event:
                return self.handle_sqs_event(event, context)
            
            else:
                logger.error("Invalid event format - no Records or job_id found")
                return {
                    "statusCode": 400,
                    "body": json.dumps({
                        "error": "Invalid event format",
                        "message": "Event must contain either 'Records' (SQS) or 'job_id' (direct)"
                    })
                }
                
        except Exception as e:
            logger.error(f"Lambda handler error: {str(e)}")
            return {
                "statusCode": 500,
                "body": json.dumps({
                    "error": "Internal server error",
                    "message": str(e)
                })
            }
        
    def handle_sqs_event(self, event: Dict[str, Any], context: Any) -> Dict[str, Any]:
        """
        Handle SQS event with multiple records
        """
        results = []
        
        for record in event['Records']:
            try:
                # Extract job_id from SQS message
                message_body = json.loads(record['body'])
                job_id = message_body.get('job_id')
                
                if not job_id:
                    logger.error(f"No job_id found in SQS message: {record['body']}")
                    results.append({
                        "status": "failed",
                        "error": "No job_id in message",
                        "messageId": record.get('messageId')
                    })
                    continue
                
                logger.info(f"Processing SQS message for job_id: {job_id}")
                
                # Process the verification job
                result = self.process_verification_job(job_id)
                results.append(result)
                
            except Exception as e:
                logger.error(f"Error processing SQS record: {str(e)}")
                results.append({
                    "status": "failed",
                    "error": str(e),
                    "messageId": record.get('messageId')
                })
        
        # Return batch results
        return {
            "statusCode": 200,
            "body": json.dumps({
                "processed_count": len(results),
                "results": results
            })
        }
    
    def process_verification_job(self, job_id: str) -> Dict[str, Any]:
        """
        Process a single verification job using mock data

        Args:
            job_id: The job identifier from SQS message

        Returns:
            Processing result dictionary
        """
        try:
            logger.info(f"Starting verification processing for job_id: {job_id}")
            
            # Perform verification
            verification_result = process_background_verification(job_id)
            
            logger.info(f"Verification processing completed for job_id: {job_id}")
            return {
                "status": "success",
                "job_id": job_id,
                "result": verification_result
            }
        except Exception as e:
            logger.error(f"Error processing verification job: {str(e)}")
            return {
                "status": "failed",
                "error": str(e),
                "job_id": job_id
            }

def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    AWS Lambda handler function
    
    Args:
        event: Lambda event containing SQS messages
        context: Lambda context object
        
    Returns:
        Processing results
    """
    try:
        processor = LambdaVerificationProcessor()
        return processor.process_event(event, context)
    except Exception as e:
        logger.error(f"Lambda handler error: {str(e)}")
        return {
            "statusCode": 500,
            "body": json.dumps({
                "error": "Lambda handler failed",
                "message": str(e)
            })
        }

# # Test event
# event = {
#     "Records": [
#         {
#             "body": "{\"job_id\": \"2548c8d8-9333-4b2c-80cd-dd7755c28918\"}"
#         }
#     ]
# }
# print(lambda_handler(event, None))