import os
import re
import time
import json
import boto3
from loguru import logger
from prompt_engineering.process_enriched_cpf import process_cpf
from prompt_engineering.process_client_input import process_client_input
from prompt_engineering.validation_prompt import generate_final_validation_prompt
from database.dynamodb_client import fetch_item_by_job_id, update_status_to_completed

start_time = time.strftime("%H:%M:%S", time.localtime())
logger.info("Starting time: ", start_time)

REGION = os.environ.get("AWS_REGION", "us-east-1")

# Bedrock runtime client
bedrock = boto3.client('bedrock-runtime', region_name=REGION)

def call_bedrock_mistral(prompt: str, model_id: str = "mistral.mistral-7b-instruct-v0:2") -> str:
    response = bedrock.invoke_model(
        modelId=model_id,
        contentType="application/json",
        accept="application/json",
        body=json.dumps({
            "prompt": prompt,
            "max_tokens": 1024,
            "temperature": 0.5,
            "top_p": 0.9
        })
    )
    body = json.loads(response['body'].read())
    return body['outputs'][0]['text']

def validate_data(payload_json):
    validation_matrix = payload_json["validation_matrix"]
    enriched_cpf = payload_json["enriched_cpf"]
    client_input = payload_json["client_input"]

    prompt = generate_final_validation_prompt(validation_matrix, enriched_cpf, client_input)
    response = call_bedrock_mistral(prompt)
    return response

# Extract the JSON object in response
def extract_json(result_text):
    try:
        match = re.search(r"\{[\s\S]*\}", result_text)
        if match:
            return json.loads(match.group())
    except json.JSONDecodeError:
        pass
    return None

def process_background_verification(job_id: str) -> str:
    raw_data = fetch_item_by_job_id(job_id)
    if raw_data is None:
        return "❌ No item found with the given job_id."

    # Load the payload
    payload = {}
    payload["enriched_cpf"] = process_cpf(raw_data["enriched_cpf"] if raw_data["enriched_cpf"] else "")
    payload["client_input"] = process_client_input(raw_data["client_input"] if raw_data["client_input"] else "")
    payload["validation_matrix"] = raw_data["validation_matrix"] if raw_data["validation_matrix"] else {}
    logger.info("Payload: ", payload)

    result = validate_data(payload)
    try:
        response = json.loads(result)
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse JSON: {str(e)}")
        response = extract_json(result)
    print("Response: ", json.dumps(response, indent=2, default=str))
    # logger.info("Response: ", response)
    
    # Update job status to completed
    update_status_to_completed(job_id, response)
    end_time = time.strftime("%H:%M:%S", time.localtime())
    logger.info("Ending time: ", end_time)
    return response