{"enriched_cpf": "\n    ```json\n    {\n        \"PersonalDetails\": {\n            \"Name\": \"<PERSON>. MOHAN RAJ\",\n            \"AlternateName\": \"\",\n            \"Mobile\": \"+91-9962617413\",\n            \"Email\": \"mohan<PERSON>.<EMAIL>\",\n            \"LinkedIn\": \"https://www.linkedin.com/in/mohan-raj-4b5077245/\",\n            \"GitHub\": \"https://github.com/mohan-1324\"\n        },\n        \"ProfessionalSummary\": {\n            \"Title\": \"AWS Infra & AWS DevOps Engineer\",\n            \"ExperienceYears\": 4,\n            \"Summary\": \"As an AWS Infra & AWS DevOps Engineer with 4 years of experience in development and management, I have hands-on experience in cloud infrastructure using AWS, and tools like Lambda, GitLab, Jenkins, SonarQube, Ansible, and Docker.\"\n        },\n        \"WorkExperience\": [\n            {\n                \"Organization\": \"Emergere Technologies\",\n                \"Designation\": \"DevOps Engineer\",\n                \"StartDate\": \"Aug 2024\",\n                \"EndDate\": \"Present\",\n                \"Domain\": [\"AWS\", \"GCP\"]\n            },\n            {\n                \"Organization\": \"HTC Global Services\",\n                \"Designation\": \"Engineer\",\n                \"StartDate\": \"Mar 2021\",\n                \"EndDate\": \"Jul 2024\",\n                \"Domain\": [\"AWS\"]\n            }\n        ],\n        \"TechnicalSkills\": {\n            \"CloudTech\": [\"AWS\", \"GCP\"],\n            \"OS\": [\"Linux\", \"Windows\"],\n            \"Scripting\": [\"Python\"],\n            \"CI_CD\": [\"GitHub\", \"GitLab\", \"Jenkins\", \"AWS CodePipeline\"],\n            \"IAC\": [\"Terraform\"],\n            \"Other\": [\"Ansible\", \"Docker\", \"SonarQube\"]\n        },\n        \"Education\": [\n            {\n                \"Qualification\": \"BCA\",\n                \"Institution\": \"Hindustan University\",\n                \"GraduationDate\": \"2015–2018\"\n            }\n        ],\n        \"Certifications\": [\"Associate Google Cloud Engineer\"],\n        \"Projects\": [\n            {\n                \"Name\": \"USLBM\",\n                \"Dates\": \"Aug 2024–Present\",\n                \"Role\": \"Cloud Infrastructure Engineer\",\n                \"Environment\": [\"AWS\", \"Azure\", \"GCP\"],\n                \"Responsibilities\": [\n                    \"Managing IAM across AWS, Azure, GCP\",\n                    \"Creating RFPs and cloud architecture\",\n                    \"Provisioning Lambda\",\n                    \"Scripting in Python\",\n                    \"Security implementation\",\n                    \"Monitoring\",\n                    \"Configuring core AWS services (EC2, ELB, ASG, VPC, VPN, S3, IAM, etc.)\",\n                    \"Enforcing SCPs\",\n                    \"Conducting RCAs and penetration tests\"\n                ]\n            },\n            {\n                \"Name\": \"Heartfulness Webapp Support\",\n                \"Dates\": \"Jul 2022–Jul 2024\",\n                \"Role\": \"Cloud Support Engineer\",\n                \"Environment\": \"AWS\",\n                \"Responsibilities\": [\n                    \"Automated deployments\",\n                    \"Maintained availability and security\",\n                    \"Used SNS, Lambda, CloudWatch\",\n                    \"Cost optimization via AWS tools\"\n                ]\n            },\n            {\n                \"Name\": \"Internal Project\",\n                \"Dates\": \"Feb 2022–Jan 2023\",\n                \"Role\": \"Cloud Support Engineer\",\n                \"Environment\": \"AWS\",\n                \"Responsibilities\": [\n                    \"Managed AWS deployments\",\n                    \"Configured S3, EC2, DB provisioning\",\n                    \"DevOps workflows\"\n                ]\n            }\n        ],\n        \"Appreciations\": [\"Maintained 95% AWS security score\", \"rapid WAF implementation during a DDoS\", \"praised for exploring FinOps and SecOps tools\"]\n    }\n    ```", "client_input": "\n    {\n        \"PersonalDetails\": {\n            \"Name\": \"MOHAN RAJ VENGATAJABATHY\",\n            \"AlternateName\": \"\",\n            \"Mobile\": \"9629540540\",\n            \"Email\": \"Jagadeesh.<PERSON><PERSON>@cognizant.com\",\n            \"LinkedIn\": \"\",\n            \"GitHub\": \"\"\n        },\n        \"ProfessionalSummary\": {\n            \"Title\": \"\",\n            \"Experience\": \"18Y 4M 8D\",\n            \"ShortSummary\": \"\"\n        },\n        \"WorkExperience\": [\n            {\n                \"Organization\": \"EMERGERE COMPUTING SOLUTIONS PRIVATE LIMITED\",\n                \"Designation\": \"\",\n                \"StartDate\": \"12-Aug-2024\",\n                \"EndDate\": \"Not_Available\",\n                \"Domain\": \"MOONLIGHTING\"\n            },\n            {\n                \"Organization\": \"HTC HOLDINGS\",\n                \"Designation\": \"\",\n                \"StartDate\": \"08-Mar-2021\",\n                \"EndDate\": \"31-Jul-2024\",\n                \"Domain\": \"MOONLIGHTING\"\n            },\n            {\n                \"Organization\": \"QUESS CORP\",\n                \"Designation\": \"\",\n                \"StartDate\": \"25-Oct-2019\",\n                \"EndDate\": \"03-Dec-2019\",\n                \"Domain\": \"MOONLIGHTING\"\n            },\n            {\n                \"Organization\": \"MENSA FOODS\",\n                \"Designation\": \"\",\n                \"StartDate\": \"27-Aug-2018\",\n                \"EndDate\": \"Not_Available\",\n                \"Domain\": \"\"\n            },\n            {\n                \"Organization\": \"PARVATHY ORTHO HOSPITAL\",\n                \"Designation\": \"\",\n                \"StartDate\": \"02-Jul-2018\",\n                \"EndDate\": \"Not_Available\",\n                \"Domain\": \"MOONLIGHTING\"\n            }\n        ],\n        \"TechnicalSkills\": {\n            \"CloudTech\": [],\n            \"OS\": [],\n            \"Scripting\": [],\n            \"CI_CD\": [],\n            \"IAC\": []\n        },\n        \"Education\": {\n            \"Qualification\": \"\",\n            \"Institution\": \"\",\n            \"GraduationDate\": \"\"\n        },\n        \"Certifications\": [],\n        \"Projects\": [],\n        \"Appreciations\": []\n    }", "validation_matrix": [{"component": "All Employment (Last 5 Years)", "S.No": "1", "vendor_colour_code": "Green", "discrepancy_statement": "Congizant Input data and Digiverifier Output Data matches", "vendor_adjudication_process": "-"}, {"component": "All Employment (Before 5 Years)", "S.No": "2", "vendor_colour_code": "Amber", "discrepancy_statement": "If overlap is identified in Disclosed/Undisclosed employment during DigiverifierValidation", "vendor_adjudication_process": "If any employment is out of scope and undisclosed the digiverifier need to share the report with the status as Amber. "}, {"component": "Onsite Employment", "S.No": "3", "vendor_colour_code": "Amber", "discrepancy_statement": "If candidate worked with overseas employment, it will not come under Digiverifier scope", "vendor_adjudication_process": "Digiverifier POC will share the report as data not found"}, {"component": "Overlap of employment up to 30 days", "S.No": "4", "vendor_colour_code": "Amber", "discrepancy_statement": "If there is an overlap of less than 30days between the previous employers which are declared in the CDF form", "vendor_adjudication_process": "DigiverifierPOC will share the report as overlap employement found "}, {"component": "Overlap of employment up to 30 days", "S.No": "5", "vendor_colour_code": "Amber", "discrepancy_statement": "If there is an overlap of less than 30days between the previous employers which are declared in the CDF form", "vendor_adjudication_process": "Digiverifier POC will share the report as overlap employement found. "}, {"component": "Overlap of employment up to 30 days", "S.No": "6", "vendor_colour_code": "Amber", "discrepancy_statement": "If there is an overlap of less than 30days between the previous employers which are not declared in the CDF form", "vendor_adjudication_process": "Digiverifier POC will share the report as overlap employement found"}, {"component": "Overlap of employment up to 30 days", "S.No": "7", "vendor_colour_code": "Amber", "discrepancy_statement": "If there is an overlap of less than 30days between the previous employers which are not declared in the CDF form", "vendor_adjudication_process": "Digiverifier POC will share the report as overlap employement found "}, {"component": "Overlap of employment greater than 30 days", "S.No": "8", "vendor_colour_code": "Red", "discrepancy_statement": "If there is an overlap of employement greater than 30days between the previous employers which are declared / not declared in the CDF form", "vendor_adjudication_process": "Digiverifier POC will share the report as overlap employement found"}, {"component": "Remittance Validation", "S.No": "9", "vendor_colour_code": "Green", "discrepancy_statement": "If data found in PF remittances when employment history found in EPFO but Data not found in ITR for disclosed employments in CDF", "vendor_adjudication_process": "-"}, {"component": "Remittance Validation", "S.No": "10", "vendor_colour_code": "Amber", "discrepancy_statement": "If data not found in PF remittances  and ITR when employment history found in EPFO for disclosed employments in CDF ", "vendor_adjudication_process": "Digiverifier POC will share the report as data not found employement "}, {"component": "Bulk Transactions", "S.No": "11", "vendor_colour_code": "Green", "discrepancy_statement": "1 bulk amount transaction found in 26AS foreach FY but,Data not foundin EPFO\nFor Eg. - For 5 years of experience in the samecompany 5 bulk transaction found in ITR for 5 FY respectively.", "vendor_adjudication_process": "-"}, {"component": "Bulk Transactions", "S.No": "12", "vendor_colour_code": "Red", "discrepancy_statement": "1 bulk amount transaction for latest Financial year found in 26AS. But Data not found in EPFO\nFor eg.,for 5 yearsof experience in the samecompany 1 latest FY bulk transaction found in ITR", "vendor_adjudication_process": "-"}, {"component": "<PERSON><PERSON> Mi<PERSON>tch", "S.No": "13", "vendor_colour_code": "Amber", "discrepancy_statement": "If there is a Tenure mismatch of less than 3 months disclosed in the CDF vs Report", "vendor_adjudication_process": "Digiverifier POC will share the report as tenure mismatch found "}, {"component": "<PERSON><PERSON> Mi<PERSON>tch", "S.No": "14", "vendor_colour_code": "Amber", "discrepancy_statement": "If there is a Tenure mismatch of more than 3 months disclosed in the application form vs Report", "vendor_adjudication_process": "Digiverifier POC will share the report as  tenure mismatch found"}, {"component": "Digiverifier DNH Database", "S.No": "15", "vendor_colour_code": "Amber", "discrepancy_statement": "If claimed employments in the application form Not Found in ITR/EPFO but found in DNH database", "vendor_adjudication_process": "Digiverifier POC will share the report as data not found"}, {"component": "Data Not Found", "S.No": "16", "vendor_colour_code": "Amber", "discrepancy_statement": "If disclosed employments in the CDF Not Found in ITR/EPFO", "vendor_adjudication_process": "Digiverifier POC will share the report as data not found"}, {"component": "Impersonation found", "S.No": "17", "vendor_colour_code": "Red", "discrepancy_statement": "If candidate used someone else PAN or UAN Credientials and if the digiverifiers finding is not matched with CDF input", "vendor_adjudication_process": "Digiverifier POC will share the report as impersonation found"}, {"component": "Undisclosed company in the last 5years", "S.No": "18", "vendor_colour_code": "Amber", "discrepancy_statement": "During Digiverifier verificaiton, if any undisclosed employment is found in the output which falls in last 5 year scope and if it is not declared in CDF", "vendor_adjudication_process": "Digiverifier POC will share the report as undisclosed emloyment found"}, {"component": "PF portal - Server down", "S.No": "19", "vendor_colour_code": "Amber  / Red", "discrepancy_statement": "If PF portal is dowm", "vendor_adjudication_process": "Vendor has to share  the intrim report based on the ITR data, once EPFO server up vendor has to reinitiate the digiverifier process and share the updated colur code"}, {"component": "Freelancer /Training ", "S.No": "20", "vendor_colour_code": "Amber", "discrepancy_statement": "If disclosed employments in the CDF found freelancer / Training ", "vendor_adjudication_process": "During digiverication if the data found in 194 JA and JB"}]}