import json

def generate_client_input_validation_prompt(raw_client_input):
    prompt = f"""
    You are a data extraction assistant. From the input background verification report below, extract the key details in structured JSON with these top-level sections:

    Ensure all relevant sections are captured including:

    1. Personal Details: Name, Alternate Name, Mobile, Email, LinkedIn, GitHub
    2. Professional Summary: Title, Experience (years), Short summary
    3. Work Experience: Organization, Designation, Start Date, End Date, Domain (if available)
    4. Technical Skills: Grouped into categories like Cloud Tech, OS, Scripting, CI/CD, IAC, etc.
    5. Education: Qualification, Institution, Graduation Date
    6. Certifications
    7. Projects: Name, Dates, Role, Environment, Responsibilities
    8. Appreciations: List of appreciation points

    Always return the output as clean JSON only without commentary or explanation.

    Background Report:
    \"\"\"{raw_client_input}\"\"\"
    """
    return prompt
