import json

def generate_final_validation_prompt(validation_matrix, enriched_cpf, client_input):
    # first_two_criteria = validation_matrix[:2]
    prompt = f"""
    You are a background verification assistant.

    Your ONLY task is to compare the employment data provided by the client against the enriched data obtained from the vendor using the validation matrix provided below.

    IMPORTANT:
    - Only use the FIRST TWO rules from the validation matrix.
    - If a discrepancy qualifies for RED or AMBER, it MUST NEVER be marked GREEN.
    - Your response MUST be in valid JSON format and MUST NOT include any explanation or commentary outside the JSON object.

    STRICT OUTPUT FORMAT:
    Return ONLY a JSON object using the format below. DO NOT return any text or explanation before or after the JSON object.

    VALIDATION MATRIX:
    {json.dumps(validation_matrix, indent=2, default=str)}

    CLIENT INPUT:
    {json.dumps(client_input, indent=2, default=str)}

    VENDOR OUTPUT:
    {json.dumps(enriched_cpf, indent=2, default=str)}

    RESPONSE FORMAT:
    ```json
    {{
    "response": [
        {{
        "field": "Field Name Here",
        "client_value": "Client’s Value",
        "vendor_value": "Vendor’s Value",
        "reason": "Short, human-readable reason for discrepancy or match.",
        "suggested_colour_code": "Red | Amber | Green",
        "confidence_score": 75
        }}
    ]
    }}
    ```
    """
    return prompt
