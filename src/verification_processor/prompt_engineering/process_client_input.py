import os
import json
import boto3
from loguru import logger
from prompt_engineering.client_input_prompt import generate_client_input_validation_prompt

REGION = os.environ.get("AWS_REGION", "us-east-1")

# Initialize AWS Bedrock Runtime client
bedrock_runtime = boto3.client(
    "bedrock-runtime",
    region_name=REGION
)

# ==== FUNCTION TO CALL BEDROCK MODEL ====
def process_client_input(raw_client_input):
    try:
        prompt = generate_client_input_validation_prompt(raw_client_input)
        response = bedrock_runtime.invoke_model(
            modelId="mistral.mistral-7b-instruct-v0:2",
            body=json.dumps({
                "prompt": prompt,
                "max_tokens": 7096,
                "temperature": 0.3,
                "top_p": 0.9,
                "stop": ["</s>"]
            }),
            contentType="application/json",
            accept="application/json"
        )
        response_body = response["body"].read().decode("utf-8")
        result = json.loads(response_body)
        processed_client_input = json.loads(result.get("outputs", [])[0]["text"]) if type(result) == str else result.get("outputs", [])[0]["text"]
        return json.loads(json.dumps(processed_client_input))
    except Exception as e:
        logger.error(f"Failed to process client input: {str(e)}")
        return None