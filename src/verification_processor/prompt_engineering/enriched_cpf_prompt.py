import json

def generate_enriched_cpf_validation_prompt(raw_enriched_cpf):
    prompt = f"""
    You are an intelligent assistant. Extract structured JSON from the resume text below. 
    Ensure all relevant sections are captured including:

    1. Personal Details: Name, Alternate Name, Mobile, Email, LinkedIn, GitHub
    2. Professional Summary: Title, Experience (years), Short summary
    3. Work Experience: Organization, Designation, Start Date, End Date, Domain (if available)
    4. Technical Skills: Grouped into categories like Cloud Tech, OS, Scripting, CI/CD, IAC, etc.
    5. Education: Qualification, Institution, Graduation Date
    6. Certifications
    7. Projects: Name, Dates, Role, Environment, Responsibilities
    8. Appreciations: List of appreciation points

    Resume Text:
    \"\"\"{raw_enriched_cpf}\"\"\"

    Always return the output as clean JSON only without commentary or explanation.
    """
    return prompt
