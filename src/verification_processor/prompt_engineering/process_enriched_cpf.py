# Using bedrock model, process the enriched_cpf and extract the key information from it using prompt engineering in structured json format
import os
import json
import boto3
from loguru import logger
from prompt_engineering.enriched_cpf_prompt import generate_enriched_cpf_validation_prompt

REGION = os.environ.get("AWS_REGION", "us-east-1")

# ====== AWS Bedrock Client Initialization ======
bedrock_runtime = boto3.client(
    "bedrock-runtime",
    region_name=REGION
)

# ====== Bedrock Request ======
def process_cpf(raw_enriched_cpf):
    try:
        prompt = generate_enriched_cpf_validation_prompt(raw_enriched_cpf)
        response = bedrock_runtime.invoke_model(
            modelId="mistral.mistral-7b-instruct-v0:2",  # For Mistral 7B Instruct
            body=json.dumps({
                "prompt": prompt,
                "max_tokens": 2048,
                "temperature": 0.3,
                "top_p": 0.9,
                "stop": ["</s>"]
            }),
            contentType="application/json",
            accept="application/json"
        )
        
        response_body = response['body'].read().decode("utf-8")
        result = json.loads(response_body)
        processed_enriched_cpf = json.loads(result.get("outputs", [])[0]["text"]) if type(result) == str else result.get("outputs", [])[0]["text"]
        return json.loads(json.dumps(processed_enriched_cpf, indent=2, default=str))
    except Exception as e:
        logger.error(f"Failed to process CPF: {str(e)}")
        return None